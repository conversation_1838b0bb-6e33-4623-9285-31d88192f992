import React, { useMemo } from "react";
import { Model, surveyLocalization } from "survey-core";
import { Survey } from "survey-react-ui";
import "survey-core/survey-core.css";
import polls from "@/constants/polls.json";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import * as SurveyTheme from "survey-core/themes";
import { useSubmitPollResponse } from "@/features/suggestions/api/usePolls";

function SurveyComponent({ poll }) {
    const navigate = useNavigate();
    const submitPollResponseMutation = useSubmitPollResponse();

    const createPollResponse = async (pollResponse, pollId, options) => {
        options.showSaveInProgress();
        try {
            const parsedID = parseInt(pollId, 10);

            await submitPollResponseMutation.mutateAsync({
                pollId: parsedID,
                pollResponse,
            });

            options.showSaveSuccess();
            navigate(`/polls?id=${pollId}`);
        } catch (error) {
            toast.error(error.message || "Failed to submit poll response");
            options.showSaveError();
            throw error;
        }
    };

    // Initialize the survey model in a Memo to prevent re-creation
    const surveyData = useMemo(() => {
        const survey = new Model(polls[poll.id - 1]);
        survey.applyTheme(SurveyTheme.LayeredDark);
        survey.onComplete.add((sender, options) => {
            const pollResponse = JSON.stringify(sender.data);
            createPollResponse(pollResponse, poll.id, options);
        });

        surveyLocalization.locales["en"] = {
            emptySurvey: "Site out of date. Please refresh the page.",
            completeText: "Submit",
        };

        return survey;
    }, [poll.id]);

    return <Survey model={surveyData} />;
}

export default SurveyComponent;
